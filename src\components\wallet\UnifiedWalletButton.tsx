"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Wallet, Copy, ExternalLink, ChevronDown } from "lucide-react";
import { useWallet } from "@solana/wallet-adapter-react";
import {
  useAccountSafe,
  useChainIdSafe,
  useDisconnectSafe,
  useConnectSafe,
} from "@/hooks/useWagmiSafe";
import { WalletButton } from "./WalletButton";
import { SolanaWalletButton } from "./SolanaWalletButton";
import { formatAddress, getChainConfig } from "@/lib/web3-config";
import {
  getSolanaNetworkConfig,
  formatSolanaAddress,
} from "@/lib/solana-config";
import { toast } from "sonner";
import { useWalletSafe } from "@/hooks/useSolanaSafe";
import {
  WalletErrorBoundary,
  useWalletErrorHandler,
} from "./WalletErrorBoundary";
import { useConnectModal } from "@rainbow-me/rainbowkit";

interface UnifiedWalletButtonProps {
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function UnifiedWalletButton({
  variant = "outline",
  size = "sm",
  className = "",
}: UnifiedWalletButtonProps) {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [isWalletTypeModalOpen, setIsWalletTypeModalOpen] = useState(false);
  const [isWalletInfoModalOpen, setIsWalletInfoModalOpen] = useState(false);
  const [isWalletConnectionModalOpen, setIsWalletConnectionModalOpen] =
    useState(false);
  const [selectedWalletType, setSelectedWalletType] = useState<
    "evm" | "solana" | null
  >(null);

  // Use wallet error handler
  const { handleWalletError } = useWalletErrorHandler();

  // RainbowKit modal hook for EVM wallet connections
  const { openConnectModal } = useConnectModal();

  // Check EVM wallet connection
  const { isConnected: isEvmConnected, address: evmAddress } = useAccountSafe();
  const chainId = useChainIdSafe();
  const { disconnect: disconnectEvm } = useDisconnectSafe();
  const { connect: connectEvm } = useConnectSafe();
  const chain = getChainConfig(chainId);

  // Check Solana wallet connection
  const {
    connected: isSolanaConnected,
    publicKey: solanaPublicKey,
    disconnect: disconnectSolana,
    wallet: solanaWallet,
    wallets: solanaWallets,
    select: selectSolanaWallet,
    connect: connectSolanaWallet,
  } = useWalletSafe();

  // Debug logging for wallet state changes
  useEffect(() => {
    console.log("🔍 EVM Wallet State Changed:", {
      isEvmConnected,
      evmAddress: evmAddress
        ? `${evmAddress.slice(0, 6)}...${evmAddress.slice(-4)}`
        : null,
      chainId,
    });
  }, [isEvmConnected, evmAddress, chainId]);

  useEffect(() => {
    console.log("🔍 Solana Wallet State Changed:", {
      isSolanaConnected,
      solanaPublicKey: solanaPublicKey
        ? `${solanaPublicKey.toString().slice(0, 6)}...${solanaPublicKey
            .toString()
            .slice(-4)}`
        : null,
      walletName: solanaWallet?.adapter?.name,
    });
  }, [isSolanaConnected, solanaPublicKey, solanaWallet]);

  // Check if any wallet is connected
  const isAnyWalletConnected =
    (isEvmConnected && evmAddress) || (isSolanaConnected && solanaPublicKey);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Auto-navigate to wallet dashboard when wallet connects in header
  useEffect(() => {
    if (
      mounted &&
      isAnyWalletConnected &&
      className?.includes("header-wallet")
    ) {
      // Longer delay to ensure connection is fully established and state is synced
      const timer = setTimeout(() => {
        console.log("Auto-navigating to wallet dashboard from header");
        router.push("/wallet");
        
        // Dispatch event to ensure wallet page is updated
        window.dispatchEvent(
          new CustomEvent("walletConnected", {
            detail: {
              evmConnected: isEvmConnected,
              solanaConnected: isSolanaConnected,
              evmAddress,
              solanaPublicKey: solanaPublicKey?.toString(),
              timestamp: Date.now(),
              walletType:
                isEvmConnected && isSolanaConnected
                  ? "both"
                  : isEvmConnected
                  ? "evm"
                  : isSolanaConnected
                  ? "solana"
                  : "none",
              connected: true
            },
          })
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [mounted, isAnyWalletConnected, className, router, isEvmConnected, isSolanaConnected, evmAddress, solanaPublicKey]);

  // Force refresh wallet dashboard when wallet connects with debouncing
  useEffect(() => {
    if (className?.includes("header-wallet")) {
      console.log("🔄 Wallet state changed in header:", {
        isAnyWalletConnected,
        isEvmConnected,
        isSolanaConnected,
        evmAddress: evmAddress
          ? `${evmAddress.slice(0, 6)}...${evmAddress.slice(-4)}`
          : null,
        solanaPublicKey: solanaPublicKey
          ? `${solanaPublicKey.toString().slice(0, 6)}...${solanaPublicKey
              .toString()
              .slice(-4)}`
          : null,
      });

      // Debounce the event to prevent rapid firing
      const debounceTimer = setTimeout(() => {
        // Trigger a custom event to notify dashboard to refresh
        window.dispatchEvent(
          new CustomEvent("walletConnected", {
            detail: {
              evmConnected: isEvmConnected,
              solanaConnected: isSolanaConnected,
              evmAddress,
              solanaPublicKey: solanaPublicKey?.toString(),
              timestamp: Date.now(),
              // Add wallet type for better debugging
              walletType:
                isEvmConnected && isSolanaConnected
                  ? "both"
                  : isEvmConnected
                  ? "evm"
                  : isSolanaConnected
                  ? "solana"
                  : "none",
              // Add connected status for easier handling in wallet page
              connected: isAnyWalletConnected
            },
          })
        );
      }, 200); // 200ms debounce

      return () => clearTimeout(debounceTimer);
    }
  }, [
    isAnyWalletConnected,
    isEvmConnected,
    isSolanaConnected,
    evmAddress,
    solanaPublicKey,
    className,
  ]);

  // Close connection modal when wallet connects
  useEffect(() => {
    if (isAnyWalletConnected && isWalletConnectionModalOpen) {
      setIsWalletConnectionModalOpen(false);
    }
  }, [isAnyWalletConnected, isWalletConnectionModalOpen]);

  // Reset selected wallet type when component unmounts or when wallets disconnect
  useEffect(() => {
    if (!isEvmConnected && !isSolanaConnected) {
      setSelectedWalletType(null);
    }
  }, [isEvmConnected, isSolanaConnected]);

  // Reset selected wallet type after a delay to allow user to interact with wallet modal
  // Only reset if no modal is open and no wallet is being connected
  useEffect(() => {
    if (
      selectedWalletType &&
      !isWalletConnectionModalOpen &&
      !isWalletTypeModalOpen &&
      !isWalletInfoModalOpen
    ) {
      // Set a timeout to reset the selected wallet type after 15 seconds
      // This gives user time to interact with wallet modal, but resets if they close it
      const timeout = setTimeout(() => {
        setSelectedWalletType(null);
      }, 15000);

      return () => clearTimeout(timeout);
    }
  }, [
    selectedWalletType,
    isWalletConnectionModalOpen,
    isWalletTypeModalOpen,
    isWalletInfoModalOpen,
  ]);

  // Handle wallet info modal close
  const handleWalletInfoModalClose = (open: boolean) => {
    setIsWalletInfoModalOpen(open);
  };

  // Handle button click
  const handleButtonClick = () => {
    if (isAnyWalletConnected) {
      // Check if we're in header context (has header-wallet class)
      if (className?.includes("header-wallet")) {
        // Navigate to wallet dashboard when connected and in header
        router.push("/wallet");
      } else {
        // Show wallet info modal when connected but not in header
        setIsWalletInfoModalOpen(true);
      }
    } else {
      // Always reset and show wallet type selection modal for new connections
      setSelectedWalletType(null);
      setIsWalletTypeModalOpen(true);
    }
  };

  // Handle disconnect
  const handleDisconnect = async () => {
    try {
      let evmDisconnected = false;
      let solanaDisconnected = false;
      let hasErrors = false;

      if (isEvmConnected) {
        try {
          console.log("Attempting to disconnect EVM wallet...");
          await disconnectEvm();
          evmDisconnected = true;
          console.log("EVM wallet disconnected successfully");
        } catch (evmError) {
          console.error("Primary EVM disconnect failed:", evmError);
          hasErrors = true;
        }
      }

      if (isSolanaConnected) {
        try {
          console.log("Attempting to disconnect Solana wallet...");
          await disconnectSolana();
          solanaDisconnected = true;
          console.log("Solana wallet disconnected successfully");
        } catch (solanaError) {
          console.error("Primary Solana disconnect failed:", solanaError);
          hasErrors = true;
        }
      }

      setIsWalletInfoModalOpen(false);

      // Dispatch wallet disconnected event to update the dashboard
      window.dispatchEvent(
        new CustomEvent("walletConnected", {
          detail: {
            evmConnected: isEvmConnected && !evmDisconnected,
            solanaConnected: isSolanaConnected && !solanaDisconnected,
            evmAddress: evmDisconnected ? null : evmAddress,
            solanaPublicKey: solanaDisconnected ? null : solanaPublicKey?.toString(),
            timestamp: Date.now(),
            walletType: "none",
            connected: false
          },
        })
      );

      // Show appropriate feedback based on results
      if (evmDisconnected && solanaDisconnected) {
        toast.success("All wallets disconnected successfully");
      } else if (evmDisconnected || solanaDisconnected) {
        const disconnectedWallets: string[] = [];
        if (evmDisconnected) disconnectedWallets.push("EVM");
        if (solanaDisconnected) disconnectedWallets.push("Solana");

        toast.success(
          `${disconnectedWallets.join(" and ")} wallet disconnected`
        );

        // Show warning if some wallets failed to disconnect
        if (hasErrors) {
          const failedWallets: string[] = [];
          if (isEvmConnected && !evmDisconnected) failedWallets.push("EVM");
          if (isSolanaConnected && !solanaDisconnected)
            failedWallets.push("Solana");

          if (failedWallets.length > 0) {
            setTimeout(() => {
              toast.error(
                `Failed to disconnect ${failedWallets.join(" and ")} wallet(s)`
              );
            }, 1000);
          }
        }
      } else if (hasErrors) {
        toast.error(
          "Failed to disconnect wallets. Please try again or disconnect manually from your wallet."
        );
      } else {
        toast.success("Wallets disconnected");
      }
    } catch (error) {
      console.error("Error in disconnect handler:", error);
      toast.error("Failed to disconnect wallet. Please try again.");
    }
  };

  // Handle modal close to prevent double popup issue
  const handleConnectionModalClose = useCallback((open: boolean) => {
    setIsWalletConnectionModalOpen(open);
    if (!open) {
      // Reset wallet type immediately when modal closes
      setSelectedWalletType(null);
    }
  }, []);

  const handleTypeModalClose = useCallback((open: boolean) => {
    setIsWalletTypeModalOpen(open);
    if (!open) {
      // Reset wallet type immediately when modal closes
      setSelectedWalletType(null);
    }
  }, []);

  // Show loading state during hydration - moved after all hooks
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size={size}
        className={`gap-2 font-semibold text-[10px] lg:text-xs transition-colors px-1.5 lg:px-2 h-8 border bg-transparent opacity-50 ${
          className?.includes("header-wallet")
            ? "text-primary-foreground border-primary-foreground/20"
            : "text-muted-foreground border-border bg-background"
        } ${className || ""}`}
        disabled
      >
        <Wallet className="h-4 w-4" />
        Connect Wallet
      </Button>
    );
  }

  // Handle copy address
  const handleCopyAddress = () => {
    const address =
      isEvmConnected && evmAddress
        ? evmAddress
        : isSolanaConnected && solanaPublicKey
        ? solanaPublicKey.toString()
        : "";

    if (address) {
      navigator.clipboard.writeText(address);
      toast.success("Address copied to clipboard");
    }
  };

  // Always show the unified button, never delegate to individual wallet components

  // Handle wallet type selection
  const handleWalletTypeSelect = (type: "evm" | "solana") => {
    setIsWalletTypeModalOpen(false);
    setSelectedWalletType(type);
    // Add a small delay to ensure the first modal is fully closed
    setTimeout(() => {
      setIsWalletConnectionModalOpen(true);
    }, 100);
  };

  // Show unified button that changes text based on connection status
  return (
    <>
      {/* Wallet Type Selection Modal */}
      <Dialog open={isWalletTypeModalOpen} onOpenChange={handleTypeModalClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Choose Wallet Type</DialogTitle>
            <DialogDescription>
              Select the type of cryptocurrency wallet you want to connect to
              JEBAKA.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 py-4">
            <Button
              variant="outline"
              className="h-16 flex flex-col gap-2 hover:bg-accent"
              onClick={() => handleWalletTypeSelect("evm")}
            >
              <div className="flex items-center gap-3">
                <div className="text-2xl">⟠</div>
                <div className="text-left">
                  <div className="font-semibold">EVM Wallets</div>
                  <div className="text-sm text-muted-foreground">
                    Ethereum, Polygon, BSC, etc.
                  </div>
                </div>
              </div>
            </Button>
            <Button
              variant="outline"
              className="h-16 flex flex-col gap-2 hover:bg-accent"
              onClick={() => handleWalletTypeSelect("solana")}
            >
              <div className="flex items-center gap-3">
                <div className="text-2xl">◎</div>
                <div className="text-left">
                  <div className="font-semibold">Solana Wallets</div>
                  <div className="text-sm text-muted-foreground">
                    Phantom, Solflare, etc.
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Wallet Connection Modal */}
      <Dialog
        open={isWalletConnectionModalOpen}
        onOpenChange={handleConnectionModalClose}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Connect {selectedWalletType === "evm" ? "EVM" : "Solana"} Wallet
            </DialogTitle>
            <DialogDescription>
              Choose your preferred wallet to connect.
            </DialogDescription>
          </DialogHeader>
          <WalletErrorBoundary>
            <div className="space-y-4">
              {selectedWalletType === "evm" ? (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground text-center">
                    Click the button below to open the wallet connection modal
                  </p>
                  <Button
                    onClick={() => {
                      // Close our modal first
                      setIsWalletConnectionModalOpen(false);
                      // Open RainbowKit modal which handles deduplication automatically
                      openConnectModal?.();
                    }}
                    className="w-full"
                    size="lg"
                  >
                    <Wallet className="w-4 h-4 mr-2" />
                    Connect EVM Wallet
                  </Button>
                  <div className="text-xs text-muted-foreground text-center">
                    Supports MetaMask, Coinbase Wallet, WalletConnect, Brave
                    Wallet, and more
                  </div>
                </div>
              ) : (
                <div className="grid gap-3">
                  {solanaWallets.map((wallet, index) => (
                    <Button
                      key={`solana-${wallet.adapter.name}-${index}`}
                      variant="outline"
                      className="justify-start gap-3 h-12"
                      onClick={async () => {
                        try {
                          console.log(
                            `🔗 Attempting to connect to Solana wallet: ${wallet.adapter.name}...`
                          );

                          if (!selectSolanaWallet || !connectSolanaWallet) {
                            console.error(
                              "❌ Solana wallet functions not available"
                            );
                            toast.error("Wallet connection not available");
                            return;
                          }

                          console.log(
                            "📱 Closing connection modal and selecting wallet..."
                          );
                          setIsWalletConnectionModalOpen(false);

                          console.log(
                            `🎯 Selecting Solana wallet: ${wallet.adapter.name}`
                          );

                          // Select wallet and wait for it to be ready
                          selectSolanaWallet(wallet.adapter.name);

                          console.log(
                            "⏳ Waiting 800ms for wallet selection to complete..."
                          );
                          await new Promise((resolve) =>
                            setTimeout(resolve, 800)
                          );

                          // Verify wallet is selected before connecting
                          if (
                            !solanaWallet ||
                            solanaWallet.adapter.name !== wallet.adapter.name
                          ) {
                            console.warn(
                              "⚠️ Wallet selection may not be complete, proceeding anyway..."
                            );
                          }

                          console.log("🚀 Connecting to Solana wallet...");
                          await connectSolanaWallet();

                          console.log(
                            "✅ Solana wallet connected successfully!"
                          );
                          toast.success(
                            `${wallet.adapter.name} connected successfully`
                          );
                        } catch (error: any) {
                          console.error("❌ Wallet connection error:", error);
                          const errorInfo = handleWalletError(
                            error,
                            wallet.adapter.name
                          );

                          switch (errorInfo.type) {
                            case "user_rejected":
                              console.log(
                                "👤 User cancelled wallet connection"
                              );
                              // Don't show error toast for user cancellation
                              // Don't reopen modal - user intentionally cancelled
                              break;
                            case "not_installed":
                              console.error(
                                "📱 Wallet not installed:",
                                wallet.adapter.name
                              );
                              toast.error(errorInfo.message);
                              setTimeout(
                                () => setIsWalletConnectionModalOpen(true),
                                100
                              );
                              break;
                            case "wallet_not_selected":
                              console.warn(
                                "⚠️ Wallet not selected:",
                                wallet.adapter.name
                              );
                              toast.error(errorInfo.message);
                              setTimeout(
                                () => setIsWalletConnectionModalOpen(true),
                                100
                              );
                              break;
                            default:
                              console.error(
                                "🚨 Unexpected wallet error:",
                                errorInfo
                              );
                              toast.error(errorInfo.message);
                              if (errorInfo.shouldRetry) {
                                setTimeout(
                                  () => setIsWalletConnectionModalOpen(true),
                                  100
                                );
                              }
                              break;
                          }
                        }
                      }}
                    >
                      {wallet.adapter.icon && (
                        <img
                          src={wallet.adapter.icon}
                          alt={wallet.adapter.name}
                          className="w-6 h-6"
                        />
                      )}
                      <span className="font-medium">{wallet.adapter.name}</span>
                      {wallet.readyState === "Installed" && (
                        <span className="ml-auto text-xs text-green-600">
                          Installed
                        </span>
                      )}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          </WalletErrorBoundary>
        </DialogContent>
      </Dialog>

      {/* Main Button */}
      <Button
        onClick={handleButtonClick}
        variant="ghost"
        size={size}
        className={`gap-2 font-semibold text-[10px] lg:text-xs transition-colors px-1.5 lg:px-2 h-8 border bg-transparent ${
          className?.includes("header-wallet")
            ? "text-primary-foreground hover:bg-primary-foreground/10 border-primary-foreground/20"
            : "text-foreground hover:bg-muted border-border bg-background"
        } ${className || ""}`}
      >
        <Wallet className="h-4 w-4" />
        {isAnyWalletConnected
          ? className?.includes("header-wallet")
            ? "Wallet Dashboard"
            : "Wallet Info"
          : "Connect Wallet"}
      </Button>
    </>
  );
}
