"use client";

import { useState, useEffect } from "react";
import { StaticContentPage } from "@/components/StaticContentPage";
import { WalletButton } from "@/components/wallet/WalletButton";
import { SolanaWalletButton } from "@/components/wallet/SolanaWalletButton";
import { useAccountSafe, useDisconnectSafe } from "@/hooks/useWagmiSafe";
import { useWalletSafe } from "@/hooks/useSolanaSafe";
import { usePersistentWallet } from "@/hooks/usePersistentWallet";
import { useSupabase } from "@/components/SessionProvider";
import { toast } from "sonner";
import { Copy } from "lucide-react";
import { WalletBalance } from "@/components/wallet/WalletBalance";
import { SolanaWalletBalance } from "@/components/wallet/SolanaWalletBalance";
import { useRouter } from "next/navigation";

function WalletPage() {
  const { address, isConnected } = useAccountSafe();
  const { session } = useSupabase();
  const [showBalances, setShowBalances] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'settings'>('overview');
  const router = useRouter();

  // Solana wallet connection
  const {
    connected: isSolanaConnected,
    publicKey: solanaPublicKey,
    wallet: solanaWallet,
    disconnect: disconnectSolana,
  } = useWalletSafe();

  // EVM wallet disconnect
  const { disconnect: disconnectEvm } = useDisconnectSafe();

  // Initialize persistent wallet functionality
  usePersistentWallet();
  
  // Define custom event interface for wallet connection
  interface WalletConnectedEvent extends CustomEvent {
    detail: {
      walletType: string;
      connected: boolean;
      evmConnected?: boolean;
      solanaConnected?: boolean;
    };
  }

  // Listen for wallet connection events from header
  useEffect(() => {
    const handleWalletConnectedEvent = (event: WalletConnectedEvent) => {
      const { walletType, connected, evmConnected, solanaConnected } = event.detail;
      console.log(`Wallet connection event received in dashboard:`, event.detail);
      
      // Refresh the page for both connect and disconnect events
      console.log(`Refreshing wallet dashboard due to ${connected ? 'connection' : 'disconnection'} event`);
      // Force a refresh of the current page to ensure wallet state is updated
      router.refresh();
      
      // Set a small delay and refresh again to ensure all wallet data is loaded
      setTimeout(() => {
        console.log(`Secondary refresh to ensure wallet data is ${connected ? 'loaded' : 'updated'}`);
        router.refresh();
      }, 1500);
    };
    
    // Add event listener for custom wallet connection events
    window.addEventListener('walletConnected', handleWalletConnectedEvent as EventListener);
    
    // Cleanup
    return () => {
      window.removeEventListener('walletConnected', handleWalletConnectedEvent as EventListener);
    };
  }, [router]);
  
  // Auto-refresh balances periodically when wallets are connected
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout | undefined;
    
    // Check if any wallet is connected
    const isAnyWalletConnected = isConnected || isSolanaConnected;
    
    if (isAnyWalletConnected && showBalances) {
      refreshInterval = setInterval(() => {
        console.log('Auto-refreshing wallet balances');
        setRefreshing(true);
        
        // Use router.refresh() to update the page data
        router.refresh();
        
        // Reset refreshing state after a short delay
        setTimeout(() => {
          setRefreshing(false);
        }, 500);
      }, 30000); // Refresh every 30 seconds
    }
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [isConnected, isSolanaConnected, showBalances, router]);

  // Check if any wallet is connected
  const isAnyWalletConnected = isConnected || isSolanaConnected;

  return (
    <StaticContentPage title="Crypto Wallet Dashboard">
      <div className="space-y-6">
        <div className="text-center bg-gradient-to-b from-primary/10 via-primary/5 to-transparent p-6 rounded-lg border-2 border-primary/20 shadow-md relative overflow-hidden">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
          
          <div className="relative z-10">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-foreground drop-shadow-sm">
              Crypto Wallet Dashboard
            </h1>
            <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
              Professional cryptocurrency wallet management with multi-chain support
            </p>
            <div className="mt-4 inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/30">
              <div className={`w-3 h-3 ${isAnyWalletConnected ? "bg-green-500" : "bg-orange-500"} rounded-full animate-pulse shadow-sm`}></div>
              <span className="text-sm font-medium">
                {isAnyWalletConnected ? "Connected" : "Disconnected"}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center mb-4">
          <div className="inline-flex items-center p-1 bg-muted rounded-lg">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${activeTab === 'overview' ? 'bg-background shadow-sm text-foreground' : 'text-muted-foreground hover:text-foreground'}`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${activeTab === 'settings' ? 'bg-background shadow-sm text-foreground' : 'text-muted-foreground hover:text-foreground'}`}
            >
              Settings
            </button>
          </div>
        </div>

        {activeTab === 'overview' && (
          <>
            {!session && (
              <div className="text-center p-8 border-2 border-dashed border-yellow-500/30 rounded-lg bg-yellow-500/5 shadow-md">
                <div className="flex flex-col items-center gap-4">
                  <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-yellow-700 dark:text-yellow-400 mb-2">Authentication Required</h3>
                    <p className="text-muted-foreground font-medium">
                      Please login to access your wallet dashboard and manage your crypto assets.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {session && (
              <div className="space-y-6">
                {isAnyWalletConnected ? (
                  <div className="p-6 border-2 border-green-500/20 bg-green-500/5 rounded-lg shadow-md relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
                    <div className="absolute -top-16 -right-16 w-32 h-32 bg-green-500/10 rounded-full blur-2xl"></div>
                    <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-emerald-500/10 rounded-full blur-2xl"></div>
                    
                    <div className="flex flex-col items-center md:flex-row md:justify-between md:items-start gap-4 relative z-10">
                      <div className="text-center md:text-left">
                        <h3 className="text-lg font-semibold text-green-700 dark:text-green-300 flex items-center gap-2">
                          <div className="p-1.5 bg-green-100 dark:bg-green-900/30 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                            </svg>
                          </div>
                          <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400">
                            Wallet Connected Successfully!
                          </span>
                        </h3>
                        <p className="text-green-600 dark:text-green-400 mt-2 flex items-center gap-2 justify-center md:justify-start">
                          <span className="inline-flex h-2 w-2 bg-green-500 rounded-full animate-pulse"></span>
                          {isConnected && isSolanaConnected
                            ? "Both EVM and Solana wallets are connected"
                            : isConnected
                            ? "EVM wallet is connected"
                            : "Solana wallet is connected"}
                        </p>
                      </div>
                      
                      <div className="flex flex-col gap-3 w-full md:w-auto md:min-w-[240px]">
                        {address && (
                          <div className="flex items-center justify-between gap-2 p-3 bg-background/80 backdrop-blur-sm rounded-lg border border-border hover:border-blue-500/50 hover:bg-blue-50/5 transition-all duration-300 group hover:shadow-md hover:shadow-blue-500/10">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 group-hover:scale-110 transition-transform shadow-sm">
                                <span className="text-lg">⟠</span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-sm font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">EVM</span>
                                <span className="text-xs text-muted-foreground">Ethereum</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <code className="text-xs bg-muted px-2 py-1 rounded font-mono">
                                {address.slice(0, 6)}...{address.slice(-4)}
                              </code>
                              <button 
                                className="p-1 text-muted-foreground hover:text-blue-500 transition-colors" 
                                onClick={() => {
                                  navigator.clipboard.writeText(address);
                                  toast.success("Address copied to clipboard");
                                }}
                              >
                                <Copy className="h-3.5 w-3.5" />
                              </button>
                            </div>
                          </div>
                        )}
                        {solanaPublicKey && (
                          <div className="flex items-center justify-between gap-2 p-3 bg-background/80 backdrop-blur-sm rounded-lg border border-border hover:border-purple-500/50 hover:bg-purple-50/5 transition-all duration-300 group hover:shadow-md hover:shadow-purple-500/10">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 group-hover:scale-110 transition-transform shadow-sm">
                                <span className="text-lg">◎</span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-sm font-medium group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Solana</span>
                                <span className="text-xs text-muted-foreground">SPL Token</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <code className="text-xs bg-muted px-2 py-1 rounded font-mono">
                                {solanaPublicKey.toString().slice(0, 6)}...{solanaPublicKey.toString().slice(-4)}
                              </code>
                              <button 
                                className="p-1 text-muted-foreground hover:text-purple-500 transition-colors" 
                                onClick={() => {
                                  navigator.clipboard.writeText(solanaPublicKey.toString());
                                  toast.success("Address copied to clipboard");
                                }}
                              >
                                <Copy className="h-3.5 w-3.5" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-6 border-2 border-primary/20 bg-primary/5 rounded-lg shadow-md relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
                    <div className="absolute -top-16 -right-16 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
                    <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl"></div>
                    
                    <div className="relative z-10">
                      <h3 className="text-xl font-semibold mb-4 text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
                        Connect Your Wallets
                      </h3>
                      <p className="text-muted-foreground mb-6 text-center max-w-md mx-auto">
                        Connect your EVM and Solana wallets to access the full
                        dashboard experience
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                        <div className="p-6 border-2 rounded-lg bg-background/80 backdrop-blur-sm hover:border-blue-500/50 hover:bg-blue-50/10 transition-all duration-300 group hover:shadow-lg hover:shadow-blue-500/10">
                          <div className="flex justify-center">
                            <div className="text-blue-500 text-3xl mb-3 group-hover:scale-110 transition-transform relative">
                              <span>⟠</span>
                              <div className="absolute inset-0 bg-blue-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                            </div>
                          </div>
                          <h4 className="font-medium mb-2 text-lg text-center group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">EVM Wallets</h4>
                          <p className="text-sm text-muted-foreground mb-4 text-center">
                            Ethereum, Polygon, BSC, Optimism, Arbitrum
                          </p>
                          <WalletButton 
                            variant="default" 
                            size="default" 
                            className="w-full relative z-10 group-hover:shadow-md transition-all duration-300 bg-gradient-to-r from-blue-500/80 to-blue-600/80 hover:from-blue-500 hover:to-blue-600 text-white border-0" 
                          />
                        </div>
                        <div className="p-6 border-2 rounded-lg bg-background/80 backdrop-blur-sm hover:border-purple-500/50 hover:bg-purple-50/10 transition-all duration-300 group hover:shadow-lg hover:shadow-purple-500/10">
                          <div className="flex justify-center">
                            <div className="text-purple-500 text-3xl mb-3 group-hover:scale-110 transition-transform relative">
                              <span>◎</span>
                              <div className="absolute inset-0 bg-purple-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                            </div>
                          </div>
                          <h4 className="font-medium mb-2 text-lg text-center group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Solana Wallets</h4>
                          <p className="text-sm text-muted-foreground mb-4 text-center">
                            Phantom, Solflare, Backpack, Glow
                          </p>
                          <SolanaWalletButton 
                            variant="default" 
                            size="default" 
                            className="w-full relative z-10 group-hover:shadow-md transition-all duration-300 bg-gradient-to-r from-purple-500/80 to-purple-600/80 hover:from-purple-500 hover:to-purple-600 text-white border-0" 
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
        
        {activeTab === 'settings' && (
          <div className="p-6 border-2 border-primary/20 bg-primary/5 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Wallet Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg bg-background">
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <span className="font-medium">Show Balances</span>
                </div>
                <div className="relative inline-block w-10 mr-2 align-middle select-none">
                  <input 
                    type="checkbox" 
                    checked={showBalances}
                    onChange={() => setShowBalances(!showBalances)}
                    className="sr-only"
                    id="toggle-balances"
                  />
                  <label 
                    htmlFor="toggle-balances"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors duration-200 ease-in-out ${showBalances ? 'bg-primary' : 'bg-gray-300'}`}
                  >
                    <span 
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out ${showBalances ? 'translate-x-4' : 'translate-x-0'}`}
                    ></span>
                  </label>
                </div>
              </div>
              
              {isAnyWalletConnected && (
                <div className="flex items-center justify-between p-3 border rounded-lg bg-background">
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span className="font-medium">Disconnect Wallets</span>
                  </div>
                  <button 
                    onClick={() => {
                      // Disconnect wallets
                      if (isConnected) disconnectEvm();
                      if (isSolanaConnected) disconnectSolana();
                      
                      // Dispatch wallet disconnected event to update the dashboard
                      window.dispatchEvent(
                        new CustomEvent("walletConnected", {
                          detail: {
                            evmConnected: false,
                            solanaConnected: false,
                            evmAddress: null,
                            solanaPublicKey: null,
                            timestamp: Date.now(),
                            walletType: "none",
                            connected: false
                          },
                        })
                      );
                      
                      // Show success message
                      toast.success("Wallets disconnected successfully");
                      
                      // Force a refresh of the current page to ensure wallet state is updated
                      router.refresh();
                      
                      // Set a small delay and refresh again to ensure all wallet data is updated
                      setTimeout(() => {
                        console.log('Secondary refresh to ensure wallet data is updated after disconnect');
                        router.refresh();
                      }, 1500);
                    }}
                    className="px-3 py-1 bg-red-500/10 text-red-500 rounded-md hover:bg-red-500/20 transition-colors"
                  >
                    Disconnect
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'overview' && (
          <>
            {/* Security Notice with enhanced styling */}
            <div className="p-6 border-2 border-orange-500/20 bg-orange-500/5 rounded-lg shadow-md overflow-hidden relative">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 bg-orange-500/5 rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 -ml-8 -mb-8 bg-orange-500/5 rounded-full blur-2xl"></div>
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-yellow-500/5"></div>
              
              <div className="flex flex-col md:flex-row items-center gap-4 relative z-10">
                <div className="bg-gradient-to-br from-orange-100 to-yellow-100 dark:from-orange-900/30 dark:to-yellow-900/30 p-3 rounded-full shadow-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-orange-600 dark:text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-yellow-600 dark:from-orange-400 dark:to-yellow-400 mb-2 text-lg flex items-center gap-2">
                    Security Notice
                    <span className="inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium rounded bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 shadow-sm">
                      Important
                    </span>
                  </h4>
                  <p className="text-orange-600 dark:text-orange-400 text-sm max-w-3xl">
                    Always verify transaction details before confirming. Never share
                    your private keys or seed phrases with anyone. Be cautious of phishing attempts and only connect to trusted dApps.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Quick Action Cards - Only shown when wallet is connected and user is logged in */}
            {/* Wallet Balances Section */}
            {isAnyWalletConnected && session && (
              <div className="space-y-6">
                {/* Wallet Balances */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold px-1 flex items-center gap-2">
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-foreground">Wallet Balances</span>
                    <div className="h-px flex-grow bg-gradient-to-r from-primary/20 to-transparent"></div>
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* EVM Wallet Balance */}
                    {isConnected && address && (
                      <div className="col-span-1">
                        <WalletBalance className="h-full" />
                      </div>
                    )}
                    
                    {/* Solana Wallet Balance */}
                    {isSolanaConnected && solanaPublicKey && (
                      <div className="col-span-1">
                        <SolanaWalletBalance className="h-full" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Quick Actions Section */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold px-1 flex items-center gap-2">
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-foreground">Quick Actions</span>
                    <div className="h-px flex-grow bg-gradient-to-r from-primary/20 to-transparent"></div>
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Send Payment Card */}
                    <div 
                      className="p-5 border-2 rounded-lg bg-background/80 backdrop-blur-sm hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 cursor-pointer shadow-sm group hover:shadow-md hover:scale-[1.02] relative overflow-hidden"
                      onClick={() => router.push('/wallet/send')}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="flex items-center gap-3 relative z-10">
                        <div className="bg-primary/10 p-2 rounded-full group-hover:scale-110 transition-transform duration-300 relative shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                          </svg>
                          <div className="absolute inset-0 bg-primary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors duration-300">Send Payment</h4>
                          <p className="text-xs text-muted-foreground">Transfer crypto to another wallet</p>
                        </div>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    </div>
                    
                    {/* Receive Payment Card */}
                    <div 
                      className="p-5 border-2 rounded-lg bg-background/80 backdrop-blur-sm hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 cursor-pointer shadow-sm group hover:shadow-md hover:scale-[1.02] relative overflow-hidden"
                      onClick={() => router.push('/wallet/receive')}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="flex items-center gap-3 relative z-10">
                        <div className="bg-primary/10 p-2 rounded-full group-hover:scale-110 transition-transform duration-300 relative shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                          </svg>
                          <div className="absolute inset-0 bg-primary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors duration-300">Receive Payment</h4>
                          <p className="text-xs text-muted-foreground">Get your wallet address and QR code</p>
                        </div>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    </div>
                    
                    {/* Transaction History Card */}
                    <div 
                      className="p-5 border-2 rounded-lg bg-background/80 backdrop-blur-sm hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 cursor-pointer shadow-sm group hover:shadow-md hover:scale-[1.02] relative overflow-hidden"
                      onClick={() => router.push('/wallet/history')}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="flex items-center gap-3 relative z-10">
                        <div className="bg-primary/10 p-2 rounded-full group-hover:scale-110 transition-transform duration-300 relative shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <div className="absolute inset-0 bg-primary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors duration-300">Transaction History</h4>
                          <p className="text-xs text-muted-foreground">View your recent transactions</p>
                        </div>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </StaticContentPage>
  );
}

export default WalletPage;
